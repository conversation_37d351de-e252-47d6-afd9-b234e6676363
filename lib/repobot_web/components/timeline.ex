defmodule RepobotWeb.Components.Timeline do
  use Phoenix.Component

  @doc """
  Renders a timeline component with sync events.

  ## Examples

      <.timeline events={@recent_activity} />

      <.timeline events={@recent_activity} max_items={3} />
  """
  attr :events, :list, required: true, doc: "List of timeline events"
  attr :max_items, :integer, default: 5, doc: "Maximum number of items to display"

  attr :orientation, :string,
    default: "timeline-vertical lg:timeline-horizontal",
    doc: "Timeline orientation classes"

  def timeline(assigns) do
    ~H"""
    <%= if Enum.empty?(@events) do %>
      <div class="p-4 text-center text-slate-500">
        <p>No recent sync activity found.</p>
      </div>
    <% else %>
      <ul class={"timeline #{@orientation}"}>
        <%= for {item, index} <- Enum.with_index(@events) |> Enum.take(@max_items) do %>
          <li>
            <%= if index > 0 do %>
              <hr class={get_timeline_hr_color(item.event_type)} />
            <% end %>

            <%= if rem(index, 2) == 0 do %>
              <!-- Even index: content on start side -->
              <div class="timeline-start timeline-box">
                <div class="text-sm font-medium">
                  {item.action}
                </div>
                <div class="text-xs text-slate-500 mt-1">
                  {item.repository}
                </div>
                <time class="text-xs text-slate-400 mt-1">
                  {format_timeline_date(item.date)}
                </time>
              </div>
              <div class="timeline-middle">
                {get_timeline_icon(item.event_type)}
              </div>
            <% else %>
              <!-- Odd index: content on end side -->
              <div class="timeline-middle">
                {get_timeline_icon(item.event_type)}
              </div>
              <div class="timeline-end timeline-box">
                <div class="text-sm font-medium">
                  {item.action}
                </div>
                <div class="text-xs text-slate-500 mt-1">
                  {item.repository}
                </div>
                <time class="text-xs text-slate-400 mt-1">
                  {format_timeline_date(item.date)}
                </time>
              </div>
            <% end %>

            <%= if index < @max_items - 1 and index < length(@events) - 1 do %>
              <hr class={get_timeline_hr_color(Enum.at(@events, index + 1).event_type)} />
            <% end %>
          </li>
        <% end %>
      </ul>
    <% end %>
    """
  end

  # Timeline helper functions
  defp get_timeline_color(event_type) do
    case event_type do
      "push" -> "border-primary bg-primary"
      "pr_opened" -> "border-success bg-success"
      "sync_direct" -> "border-accent bg-accent"
      _ -> "border-base-300 bg-base-300"
    end
  end

  defp get_timeline_hr_color(event_type) do
    case event_type do
      "push" -> "bg-primary"
      "pr_opened" -> "bg-success"
      "sync_direct" -> "bg-accent"
      _ -> ""
    end
  end

  defp get_timeline_icon(event_type) do
    case event_type do
      "push" ->
        Phoenix.HTML.raw("""
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="text-primary h-5 w-5">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
        </svg>
        """)

      "pr_opened" ->
        Phoenix.HTML.raw("""
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="text-success h-5 w-5">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
        </svg>
        """)

      "sync_direct" ->
        Phoenix.HTML.raw("""
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="text-accent h-5 w-5">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
        </svg>
        """)

      _ ->
        Phoenix.HTML.raw("""
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
        </svg>
        """)
    end
  end

  defp format_timeline_date(date_string) do
    # Convert relative time to a short format for timeline
    case date_string do
      "just now" ->
        "now"

      date when is_binary(date) ->
        date
        |> String.replace(" ago", "")
        |> String.replace("minute", "min")
        |> String.replace("hour", "hr")
        |> String.replace("day", "d")
        |> String.replace("week", "w")
        |> String.replace("month", "mo")
        |> String.replace("year", "yr")
        |> String.replace("s", "")

      _ ->
        "unknown"
    end
  end
end
