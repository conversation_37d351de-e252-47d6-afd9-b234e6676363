defmodule RepobotWeb.Components.Timeline do
  use Phoenix.Component

  @doc """
  Renders a timeline component with sync events.

  ## Examples

      <.timeline events={@recent_activity} />

      <.timeline events={@recent_activity} max_items={3} />
  """
  attr :events, :list, required: true, doc: "List of timeline events"
  attr :max_items, :integer, default: 5, doc: "Maximum number of items to display"

  attr :orientation, :string,
    default: "timeline-vertical lg:timeline-horizontal",
    doc: "Timeline orientation classes"

  def timeline(assigns) do
    ~H"""
    <%= if Enum.empty?(@events) do %>
      <div class="p-4 text-center text-slate-500">
        <p>No recent sync activity found.</p>
      </div>
    <% else %>
      <ul class={"timeline #{@orientation}"}>
        <%= for {item, index} <- Enum.with_index(@events) |> Enum.take(@max_items) do %>
          <li>
            <%= if index > 0 do %>
              <hr class={get_timeline_hr_color(item.event_type)} />
            <% end %>

            <%= if rem(index, 2) == 0 do %>
              <!-- Even index: content on start side -->
              <div class="timeline-start timeline-box">
                <div class="text-sm font-medium">
                  {item.action}
                </div>
                <div class="text-xs text-slate-500 mt-1">
                  {item.repository}
                </div>
                <time class="text-xs text-slate-400 mt-1">
                  {format_timeline_date(item.date)}
                </time>
              </div>
              <div class="timeline-middle">
                {get_timeline_icon(item.event_type)}
              </div>
            <% else %>
              <!-- Odd index: content on end side -->
              <div class="timeline-middle">
                {get_timeline_icon(item.event_type)}
              </div>
              <div class="timeline-end timeline-box">
                <div class="text-sm font-medium">
                  {item.action}
                </div>
                <div class="text-xs text-slate-500 mt-1">
                  {item.repository}
                </div>
                <time class="text-xs text-slate-400 mt-1">
                  {format_timeline_date(item.date)}
                </time>
              </div>
            <% end %>

            <%= if index < @max_items - 1 and index < length(@events) - 1 do %>
              <hr class={get_timeline_hr_color(Enum.at(@events, index + 1).event_type)} />
            <% end %>
          </li>
        <% end %>
      </ul>
    <% end %>
    """
  end

  # Timeline helper functions
  defp get_timeline_hr_color(event_type) do
    case event_type do
      "push" -> "bg-primary"
      "pr_opened" -> "bg-success"
      "sync_direct" -> "bg-accent"
      _ -> ""
    end
  end

  defp get_timeline_icon(event_type) do
    case event_type do
      "push" ->
        Phoenix.HTML.raw("""
        <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="w-3 h-3 text-primary-content">
            <path d="M8.75 3.75H5.5a.75.75 0 0 0-.75.75v7a.75.75 0 0 0 .75.75h7a.75.75 0 0 0 .75-.75V8.5a.75.75 0 0 0-1.5 0v2.75h-5.5V5.25H8.5a.75.75 0 0 0 0-1.5Z" />
            <path d="M12.72 2.03a.75.75 0 0 1 0 1.06L7.06 8.75a.75.75 0 0 1-1.06-1.06l5.66-5.66a.75.75 0 0 1 1.06 0Z" />
            <path d="M11.5 1.25a.75.75 0 0 1 .75.75v3a.75.75 0 0 1-.75.75h-3a.75.75 0 0 1 0-1.5h2.25V2a.75.75 0 0 1 .75-.75Z" />
          </svg>
        </div>
        """)

      "pr_opened" ->
        Phoenix.HTML.raw("""
        <div class="w-6 h-6 bg-success rounded-full flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="w-3 h-3 text-success-content">
            <path d="M6.22 8.72a.75.75 0 0 0 1.06 1.06l5.22-5.22v1.69a.75.75 0 0 0 1.5 0V3.75a.75.75 0 0 0-.75-.75h-2.5a.75.75 0 0 0 0 1.5h1.69L6.22 8.72Z" />
            <path d="M3.5 6.75c0-.69.56-1.25 1.25-1.25H7A.75.75 0 0 0 7 4H4.75A2.75 2.75 0 0 0 2 6.75v4.5A2.75 2.75 0 0 0 4.75 14h4.5A2.75 2.75 0 0 0 12 11.25V9a.75.75 0 0 0-1.5 0v2.25c0 .69-.56 1.25-1.25 1.25h-4.5c-.69 0-1.25-.56-1.25-1.25v-4.5Z" />
          </svg>
        </div>
        """)

      "sync_direct" ->
        Phoenix.HTML.raw("""
        <div class="w-6 h-6 bg-accent rounded-full flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="w-3 h-3 text-accent-content">
            <path fill-rule="evenodd" d="M13.836 2.477a.75.75 0 0 1 .487.727 7.5 7.5 0 0 1-7.75 7.750.75.75 0 0 1-.033-1.5 6 6 0 0 0 6.239-6.239H10.75a.75.75 0 0 1 0-1.5h2.25a.75.75 0 0 1 .75.75v2.25a.75.75 0 0 1-1.5 0V3.31a.75.75 0 0 1 .586-.832ZM2.164 13.523a.75.75 0 0 1-.487-.727 7.5 7.5 0 0 1 7.75-********** 0 0 1 .033 1.5 6 6 0 0 0-6.239 6.239H5.25a.75.75 0 0 1 0 1.5H3a.75.75 0 0 1-.75-.75v-2.25a.75.75 0 0 1 1.5 0v1.455a.75.75 0 0 1-.586.832Z" clip-rule="evenodd" />
          </svg>
        </div>
        """)

      _ ->
        Phoenix.HTML.raw("""
        <div class="w-6 h-6 bg-base-300 rounded-full flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="w-3 h-3 text-base-content">
            <path fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14Zm0-10a.75.75 0 0 1 .75.75v2.5h2.5a.75.75 0 0 1 0 1.5h-2.5v2.5a.75.75 0 0 1-1.5 0v-2.5h-2.5a.75.75 0 0 1 0-1.5h2.5v-2.5A.75.75 0 0 1 8 5Z" clip-rule="evenodd" />
          </svg>
        </div>
        """)
    end
  end

  defp format_timeline_date(date_string) do
    # Convert relative time to a short format for timeline
    case date_string do
      "just now" ->
        "now"

      date when is_binary(date) ->
        date
        |> String.replace(" ago", "")
        |> String.replace("minute", "min")
        |> String.replace("hour", "hr")
        |> String.replace("day", "d")
        |> String.replace("week", "w")
        |> String.replace("month", "mo")
        |> String.replace("year", "yr")
        |> String.replace("s", "")

      _ ->
        "unknown"
    end
  end
end
