defmodule RepobotWeb.Components.Timeline do
  use Phoenix.Component

  @doc """
  Renders a timeline component with sync events.

  ## Examples

      <.timeline events={@recent_activity} />

      <.timeline events={@recent_activity} max_items={3} />
  """
  attr :events, :list, required: true, doc: "List of timeline events"
  attr :max_items, :integer, default: 5, doc: "Maximum number of items to display"

  attr :orientation, :string,
    default: "timeline-vertical lg:timeline-horizontal",
    doc: "Timeline orientation classes"

  def timeline(assigns) do
    ~H"""
    <%= if Enum.empty?(@events) do %>
      <div class="p-4 text-center text-slate-500">
        <p>No recent sync activity found.</p>
      </div>
    <% else %>
      <ul class={"timeline #{@orientation}"}>
        <%= for {item, index} <- Enum.with_index(@events) |> Enum.take(@max_items) do %>
          <li>
            <%= if index > 0 do %>
              <hr class={get_timeline_hr_color(item.event_type)} />
            <% end %>

            <%= if rem(index, 2) == 0 do %>
              <!-- Even index: content on start side -->
              <div class="timeline-start">
                <div class="timeline-box group cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 hover:z-10 relative">
                  <!-- Compact view -->
                  <div class="group-hover:hidden">
                    <div class="text-xs font-medium truncate max-w-24">
                      {get_event_type_label(item.event_type)}
                    </div>
                    <div class="text-xs text-slate-400 truncate max-w-24">
                      {String.split(item.repository, "/") |> List.last()}
                    </div>
                  </div>
                  
    <!-- Expanded view on hover -->
                  <div class="hidden group-hover:block min-w-64">
                    <div class="text-sm font-medium">
                      {item.action}
                    </div>
                    <div class="text-xs text-slate-500 mt-1">
                      {item.repository}
                    </div>
                    <time class="text-xs text-slate-400 mt-1">
                      {format_timeline_date(item.date)}
                    </time>
                    <%= if item.commit_message do %>
                      <div class="text-xs text-slate-600 mt-2 p-2 bg-slate-50 rounded border-l-2 border-slate-200">
                        {item.commit_message}
                      </div>
                    <% end %>
                  </div>
                </div>
              </div>
              <div class="timeline-middle">
                {get_timeline_icon(item.event_type)}
              </div>
            <% else %>
              <!-- Odd index: content on end side -->
              <div class="timeline-middle">
                {get_timeline_icon(item.event_type)}
              </div>
              <div class="timeline-end">
                <div class="timeline-box group cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 hover:z-10 relative">
                  <!-- Compact view -->
                  <div class="group-hover:hidden">
                    <div class="text-xs font-medium truncate max-w-24">
                      {get_event_type_label(item.event_type)}
                    </div>
                    <div class="text-xs text-slate-400 truncate max-w-24">
                      {String.split(item.repository, "/") |> List.last()}
                    </div>
                  </div>
                  
    <!-- Expanded view on hover -->
                  <div class="hidden group-hover:block min-w-64">
                    <div class="text-sm font-medium">
                      {item.action}
                    </div>
                    <div class="text-xs text-slate-500 mt-1">
                      {item.repository}
                    </div>
                    <time class="text-xs text-slate-400 mt-1">
                      {format_timeline_date(item.date)}
                    </time>
                    <%= if item.commit_message do %>
                      <div class="text-xs text-slate-600 mt-2 p-2 bg-slate-50 rounded border-l-2 border-slate-200">
                        {item.commit_message}
                      </div>
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>

            <%= if index < @max_items - 1 and index < length(@events) - 1 do %>
              <hr class={get_timeline_hr_color(Enum.at(@events, index + 1).event_type)} />
            <% end %>
          </li>
        <% end %>
      </ul>
    <% end %>
    """
  end

  # Timeline helper functions
  defp get_timeline_hr_color(event_type) do
    case event_type do
      "push" -> "bg-primary"
      "pr_opened" -> "bg-success"
      "sync_direct" -> "bg-accent"
      _ -> ""
    end
  end

  defp get_event_type_label(event_type) do
    case event_type do
      "push" -> "Push"
      "pr_opened" -> "PR"
      "sync_direct" -> "Sync"
      _ -> "Event"
    end
  end

  defp get_timeline_icon(event_type) do
    case event_type do
      "push" ->
        Phoenix.HTML.raw("""
        <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
          <span class="hero-arrow-up-tray w-3 h-3 text-primary-content"></span>
        </div>
        """)

      "pr_opened" ->
        Phoenix.HTML.raw("""
        <div class="w-6 h-6 bg-success rounded-full flex items-center justify-center">
          <span class="hero-arrow-top-right-on-square w-3 h-3 text-success-content"></span>
        </div>
        """)

      "sync_direct" ->
        Phoenix.HTML.raw("""
        <div class="w-6 h-6 bg-accent rounded-full flex items-center justify-center">
          <span class="hero-arrow-path w-3 h-3 text-accent-content"></span>
        </div>
        """)

      _ ->
        Phoenix.HTML.raw("""
        <div class="w-6 h-6 bg-base-300 rounded-full flex items-center justify-center">
          <span class="hero-plus-circle w-3 h-3 text-base-content"></span>
        </div>
        """)
    end
  end

  defp format_timeline_date(date_string) do
    # Convert relative time to a short format for timeline
    case date_string do
      "just now" ->
        "now"

      date when is_binary(date) ->
        date
        |> String.replace(" ago", "")
        |> String.replace("minute", "min")
        |> String.replace("hour", "hr")
        |> String.replace("day", "d")
        |> String.replace("week", "w")
        |> String.replace("month", "mo")
        |> String.replace("year", "yr")
        |> String.replace("s", "")

      _ ->
        "unknown"
    end
  end
end
