defmodule RepobotWeb.Components.Timeline do
  use Phoenix.Component

  @doc """
  Renders a timeline component with sync events.

  ## Examples

      <.timeline events={@recent_activity} />

      <.timeline events={@recent_activity} max_items={3} />
  """
  attr :events, :list, required: true, doc: "List of timeline events"
  attr :max_items, :integer, default: 5, doc: "Maximum number of items to display"

  attr :orientation, :string,
    default: "timeline-vertical lg:timeline-horizontal",
    doc: "Timeline orientation classes"

  def timeline(assigns) do
    ~H"""
    <%= if Enum.empty?(@events) do %>
      <div class="p-4 text-center text-slate-500">
        <p>No recent sync activity found.</p>
      </div>
    <% else %>
      <ul class={"timeline #{@orientation}"}>
        <%= for {item, index} <- Enum.with_index(@events) |> Enum.take(@max_items) do %>
          <li>
            <%= if index > 0 do %>
              <hr class={get_timeline_hr_color(item.event_type)} />
            <% end %>

            <%= if rem(index, 2) == 0 do %>
              <!-- Even index: content on start side -->
              <div class="timeline-start timeline-box">
                <div class="text-sm font-medium">
                  {item.action}
                </div>
                <div class="text-xs text-slate-500 mt-1">
                  {item.repository}
                </div>
                <time class="text-xs text-slate-400 mt-1">
                  {format_timeline_date(item.date)}
                </time>
              </div>
              <div class="timeline-middle">
                {get_timeline_icon(item.event_type)}
              </div>
            <% else %>
              <!-- Odd index: content on end side -->
              <div class="timeline-middle">
                {get_timeline_icon(item.event_type)}
              </div>
              <div class="timeline-end timeline-box">
                <div class="text-sm font-medium">
                  {item.action}
                </div>
                <div class="text-xs text-slate-500 mt-1">
                  {item.repository}
                </div>
                <time class="text-xs text-slate-400 mt-1">
                  {format_timeline_date(item.date)}
                </time>
              </div>
            <% end %>

            <%= if index < @max_items - 1 and index < length(@events) - 1 do %>
              <hr class={get_timeline_hr_color(Enum.at(@events, index + 1).event_type)} />
            <% end %>
          </li>
        <% end %>
      </ul>
    <% end %>
    """
  end

  # Timeline helper functions
  defp get_timeline_hr_color(event_type) do
    case event_type do
      "push" -> "bg-primary"
      "pr_opened" -> "bg-success"
      "sync_direct" -> "bg-accent"
      _ -> ""
    end
  end

  defp get_timeline_icon(event_type) do
    case event_type do
      "push" ->
        Phoenix.HTML.raw("""
        <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="w-3 h-3 text-primary-content">
            <path d="M3.105 2.289a.75.75 0 00-.826.95l1.414 4.925A1.5 1.5 0 005.135 9.25h6.115a.75.75 0 010 1.5H5.135a1.5 1.5 0 00-1.442 1.086l-1.414 4.926a.75.75 0 00.826.95 28.896 28.896 0 0015.293-7.154.75.75 0 000-1.115A28.897 28.897 0 003.105 2.289z" />
          </svg>
        </div>
        """)

      "pr_opened" ->
        Phoenix.HTML.raw("""
        <div class="w-6 h-6 bg-success rounded-full flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="w-3 h-3 text-success-content">
            <path fill-rule="evenodd" d="M6.22 3.22a.75.75 0 011.06 0l4.25 4.25a.75.75 0 010 1.06l-4.25 4.25a.75.75 0 01-1.06-1.06L9.94 8 6.22 4.28a.75.75 0 010-1.06z" clip-rule="evenodd" />
            <path fill-rule="evenodd" d="M3 8a.75.75 0 01.75-.75h7.5a.75.75 0 010 1.5h-7.5A.75.75 0 013 8z" clip-rule="evenodd" />
          </svg>
        </div>
        """)

      "sync_direct" ->
        Phoenix.HTML.raw("""
        <div class="w-6 h-6 bg-accent rounded-full flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="w-3 h-3 text-accent-content">
            <path fill-rule="evenodd" d="M13.836 2.477a.75.75 0 01.487.727 7.5 7.5 0 01-7.75 7.750.75.75 0 01-.033-1.5 6 6 0 006.239-6.239H10.75a.75.75 0 010-1.5h2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3.31a.75.75 0 01.586-.832zM2.164 13.523a.75.75 0 01-.487-.727 7.5 7.5 0 017.75-********** 0 01.033 1.5 6 6 0 00-6.239 6.239h2.029a.75.75 0 010 1.5H2.75a.75.75 0 01-.75-.75v-2.25a.75.75 0 011.5 0v1.455a.75.75 0 01-.586.832z" clip-rule="evenodd" />
          </svg>
        </div>
        """)

      _ ->
        Phoenix.HTML.raw("""
        <div class="w-6 h-6 bg-base-300 rounded-full flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="w-3 h-3 text-base-content">
            <path fill-rule="evenodd" d="M15 8a6.973 6.973 0 00-1.71-4.584l-1.061 1.061A5.5 5.5 0 0114 8a5.5 5.5 0 01-5.5 5.5 5.5 5.5 0 01-5.5-5.5 5.5 5.5 0 015.5-5.5c1.272 0 2.44.432 3.375 1.158l-1.061 1.061A3.5 3.5 0 008.5 4a3.5 3.5 0 00-3.5 3.5 3.5 3.5 0 003.5 3.5 3.5 3.5 0 003.5-3.5z" clip-rule="evenodd" />
          </svg>
        </div>
        """)
    end
  end

  defp format_timeline_date(date_string) do
    # Convert relative time to a short format for timeline
    case date_string do
      "just now" ->
        "now"

      date when is_binary(date) ->
        date
        |> String.replace(" ago", "")
        |> String.replace("minute", "min")
        |> String.replace("hour", "hr")
        |> String.replace("day", "d")
        |> String.replace("week", "w")
        |> String.replace("month", "mo")
        |> String.replace("year", "yr")
        |> String.replace("s", "")

      _ ->
        "unknown"
    end
  end
end
